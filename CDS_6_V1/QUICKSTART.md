# Quick Start Guide - CDS_6_V1

## 🚀 Getting Started in 5 Minutes

### 1. Verify Setup
```bash
cd CDS_6_V1
python test_setup.py
```

### 2. Choose Your Model Provider

#### Option A: Using Ollama (Recommended for Local Development)
```bash
# Switch to Ollama
python switch_model.py ollama

# Make sure Ollama is running and models are available
ollama pull deepseek-r1:14b
ollama pull mxbai-embed-large:latest
```

#### Option B: Using Hugging Face
```bash
# Switch to Hugging Face
python switch_model.py huggingface

# Set your HF token in .env file
# HUGGINGFACE_API_TOKEN=your_token_here
```

### 3. Prepare Your Data
```bash
# Create directory structure like this:
mkdir -p data/policy_docs/"Applied Aviation Corporate Aircraft"
mkdir -p data/policy_docs/"Commercial Property"
# ... add more policy types as needed

# Add your PDF files to the appropriate folders
```

### 4. Run the Application
```bash
python main.py
```

## 📁 Project Structure
```
CDS_6_V1/
├── main.py                 # Main application
├── config.py              # Configuration management
├── .env                   # Environment variables
├── models/
│   └── model_factory.py   # Model creation (Ollama/HF)
├── core/
│   └── policy_mappings.py # Policy field definitions
├── utils/
│   └── logging_config.py  # Logging setup
├── data/                  # Your PDF documents
├── logs/                  # Application logs
└── temp/                  # Temporary files
```

## 🔧 Configuration

### Environment Variables (.env)
```bash
# Model provider
PROVIDER=ollama  # or 'huggingface'

# Ollama settings
OLLAMA_MODEL_NAME=deepseek-r1:14b
OLLAMA_EMBEDDING_MODEL=mxbai-embed-large:latest

# Hugging Face settings
HF_MODEL_NAME=microsoft/DialoGPT-medium
HF_EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
HUGGINGFACE_API_TOKEN=your_token

# Data paths
ROOT_FOLDER=./data/policy_docs
```

## 🎯 Supported Policy Types

1. Applied Aviation Corporate Aircraft
2. Applied Aviation Commercial General Liability
3. Applied Entertainment Sport Excess Liability
4. Applied Entertainment Sports General Liability
5. Applied Financial Lines Excess Liability
6. Applied Financial Lines Management Liability
7. Applied Financial Lines Professional Liability
8. Applied Specialty Underwriters Commercial Excess Liability
9. Applied United Risk Logistics
10. Outfitters & Guides Commercial General Liability
11. Commercial Property

## 📊 Output

The system generates:
- **JSON results file**: `enhanced_insurance_classification_[provider]_[k].json`
- **Detailed logs**: `logs/app.log`
- **Performance metrics**: Success rate and processing statistics

## 🛠️ Utilities

### Check Status
```bash
python switch_model.py status
```

### Switch Models
```bash
python switch_model.py ollama      # Switch to Ollama
python switch_model.py huggingface # Switch to Hugging Face
```

### Test Setup
```bash
python test_setup.py  # Verify all dependencies and configuration
```

## 🔍 Troubleshooting

### Common Issues

1. **"Model not found"**
   - For Ollama: `ollama pull [model-name]`
   - For HF: Check your API token

2. **"Dependencies missing"**
   ```bash
   pip install -r requirements.txt
   ```

3. **"Configuration error"**
   - Check your `.env` file
   - Run `python switch_model.py status`

4. **"No PDF files found"**
   - Ensure PDFs are in `data/policy_docs/[PolicyType]/`
   - Check `ROOT_FOLDER` in `.env`

### Performance Tips

1. **Reduce memory usage**: Lower `MAX_CONTEXT_CHARS` in `.env`
2. **Improve accuracy**: Adjust `MIN_CHUNK_RELEVANCE_SCORE`
3. **Speed up processing**: Use smaller models or enable GPU

## 📝 Example Usage

```python
# Direct usage in Python
from main import enhanced_classify_insurance_policy
from models.model_factory import get_models
from config import settings

# Initialize models
llm, embeddings = get_models(settings)

# Classify a single document
result = enhanced_classify_insurance_policy(
    file_path="path/to/policy.pdf",
    embeddings=embeddings,
    llm=llm,
    expected_policy="Applied Aviation Corporate Aircraft"
)

print(result)
```

## 🎉 Success!

If everything is working correctly, you should see:
- ✅ All tests passing in `test_setup.py`
- 📄 PDF files being processed
- 📊 Classification results with confidence scores
- 📋 Extracted fields for each policy type
- 📈 Success rate statistics

Happy classifying! 🚀
