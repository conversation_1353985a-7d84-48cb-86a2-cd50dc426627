"""
Logging configuration for the application.
"""
import logging
import sys
from pathlib import Path
from typing import Optional

from CDS_6_V1.config import Settings


def setup_logging(settings: Settings) -> logging.Logger:
    """Set up logging configuration."""
    
    # Create logs directory if it doesn't exist
    log_file_path = Path(settings.log_file)
    log_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, settings.log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(settings.log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger("insurance_classifier")
    logger.info(f"Logging initialized. Level: {settings.log_level}")
    logger.info(f"Log file: {settings.log_file}")
    
    return logger


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """Get a logger instance."""
    return logging.getLogger(name or "insurance_classifier")
