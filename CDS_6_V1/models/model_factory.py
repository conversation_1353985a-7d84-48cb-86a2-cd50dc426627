"""
Model factory for creating LLM and embedding models from different providers.
"""
import os
from typing import Union, Any

from huggingface_hub import login
from langchain_core.language_models import BaseChatModel
from langchain_core.embeddings import Embeddings
from langchain_huggingface import ChatHuggingFace, HuggingFacePipeline
from CDS_6_V1.config import Settings


class ModelFactory:
    """Factory class for creating models from different providers."""
    
    @staticmethod
    def create_llm(settings: Settings) -> BaseChatModel:
        """Create LLM based on the configured provider."""
        if settings.model_provider.lower() == "ollama":
            return ModelFactory._create_ollama_llm(settings)
        elif settings.model_provider.lower() == "huggingface":
            return ModelFactory._create_huggingface_llm(settings)
        else:
            raise ValueError(f"Unsupported model provider: {settings.model_provider}")
    
    @staticmethod
    def create_embeddings(settings: Settings) -> Embeddings:
        """Create embeddings based on the configured provider."""
        if settings.embedding_provider.lower() == "ollama":
            return ModelFactory._create_ollama_embeddings(settings)
        elif settings.embedding_provider.lower() == "huggingface":
            return ModelFactory._create_huggingface_embeddings(settings)
        else:
            raise ValueError(f"Unsupported embedding provider: {settings.model_provider}")
    
    @staticmethod
    def _create_ollama_llm(settings: Settings) -> BaseChatModel:
        """Create Ollama LLM."""
        try:
            from langchain_ollama import ChatOllama
            
            llm = ChatOllama(
                model=settings.ollama_model_name,
                base_url=settings.ollama_base_url,
                temperature=0.0,
                extract_reasoning=False,
                num_predict=-1,
                top_k=5,
                top_p=0.3,
                format="json",
                think=False
            )
            print(f"✅ Ollama LLM initialized: {settings.ollama_model_name}")
            return llm
        except ImportError:
            raise ImportError("langchain_ollama is required for Ollama models. Install with: pip install langchain-ollama")
        except Exception as e:
            raise RuntimeError(f"Failed to initialize Ollama LLM: {e}")
    
    @staticmethod
    def _create_ollama_embeddings(settings: Settings) -> Embeddings:
        """Create Ollama embeddings."""
        try:
            from langchain_ollama.embeddings import OllamaEmbeddings
            
            embeddings = OllamaEmbeddings(
                model=settings.ollama_embedding_model,
                base_url=settings.ollama_base_url
            )
            print(f"✅ Ollama embeddings initialized: {settings.ollama_embedding_model}")
            return embeddings
        except ImportError:
            raise ImportError("langchain_ollama is required for Ollama embeddings. Install with: pip install langchain-ollama")
        except Exception as e:
            raise RuntimeError(f"Failed to initialize Ollama embeddings: {e}")
    
    @staticmethod
    def _create_huggingface_llm(settings: Settings) -> BaseChatModel:
        """Create Hugging Face LLM."""
        try:
            from langchain_huggingface import ChatHuggingFace, HuggingFacePipeline
            from transformers import pipeline

            token = os.getenv("HUGGINGFACE_API_TOKEN")
            login(token)

            # Create the pipeline
            hf_pipeline = pipeline(
                "text-generation",
                model=settings.hf_model_name,
                # token="*************************************",
                # device_map="auto",
                # torch_dtype="auto",
                # trust_remote_code=True,
            )
            
            # Wrap in LangChain
            #hf_llm = HuggingFacePipeline('text-generation',)
            hf_llm = HuggingFacePipeline.from_model_id(
                model_id="nvidia/Llama-3.1-Nemotron-Nano-4B-v1.1",
                task="text-generation",
            )
            llm = ChatHuggingFace(llm=hf_llm)
            
            print(f"✅ Hugging Face LLM initialized: {settings.hf_model_name}")
            return llm
        except ImportError:
            raise ImportError("langchain_huggingface and transformers are required for HF models. Install with: pip install langchain-huggingface transformers")
        except Exception as e:
            raise RuntimeError(f"Failed to initialize Hugging Face LLM: {e}")
    
    @staticmethod
    def _create_huggingface_embeddings(settings: Settings) -> Embeddings:
        """Create Hugging Face embeddings."""
        try:
            from langchain_huggingface import HuggingFaceEmbeddings
            
            embeddings = HuggingFaceEmbeddings(
                model_name=settings.hf_embedding_model,
                model_kwargs={'device': 'cpu'},  # Change to 'cuda' if GPU available
                encode_kwargs={'normalize_embeddings': True}
            )
            print(f"✅ Hugging Face embeddings initialized: {settings.hf_embedding_model}")
            return embeddings
        except ImportError:
            raise ImportError("langchain_huggingface is required for HF embeddings. Install with: pip install langchain-huggingface")
        except Exception as e:
            raise RuntimeError(f"Failed to initialize Hugging Face embeddings: {e}")


def get_models(settings: Settings) -> tuple[BaseChatModel, Embeddings]:
    """Get both LLM and embeddings models."""
    llm = ModelFactory.create_llm(settings)
    embeddings = ModelFactory.create_embeddings(settings)
    return llm, embeddings
