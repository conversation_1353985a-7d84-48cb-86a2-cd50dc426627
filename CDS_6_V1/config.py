"""
Configuration management for the Insurance Policy Classification system.
"""
import os
from pathlib import Path
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv
from huggingface_hub import login

# Load environment variables
load_dotenv()


class Settings(BaseSettings):
    """Application settings with environment variable support."""

    # Model Configuration
    provider: str = Field(default="ollama", description="Model provider: 'ollama' or 'huggingface'")
    embedding_provider: str = Field(default="ollama", description="Model provider: 'ollama' or 'huggingface'")

    # Ollama Configuration
    ollama_model_name: str = Field(default="deepseek-r1:14b", description="Ollama model name")
    ollama_embedding_model: str = Field(default="mxbai-embed-large:latest", description="Ollama embedding model")
    ollama_base_url: str = Field(default="http://localhost:11434", description="Ollama base URL")

    # Hugging Face Configuration
    hf_model_name: str = Field(default="microsoft/DialoGPT-medium", description="Hugging Face model name")
    hf_embedding_model: str = Field(default="sentence-transformers/all-MiniLM-L6-v2", description="HF embedding model")
    huggingface_api_token: Optional[str] = Field(default=None, description="Hugging Face API token")

    # Data Paths
    root_folder: str = Field(default="./data/policy_docs", description="Root folder for policy documents")
    temp_vector_store_path: str = Field(default="./temp/faiss_collection", description="Temporary vector store path")

    # Processing Configuration
    top_k_value: int = Field(default=3, description="Number of top results to retrieve")
    max_context_chars: int = Field(default=22000, description="Maximum context characters")
    min_chunk_relevance_score: float = Field(default=0.3, description="Minimum chunk relevance score")
    enable_context_deduplication: bool = Field(default=True, description="Enable context deduplication")
    enable_schema_validation: bool = Field(default=True, description="Enable schema validation")

    # Logging
    log_level: str = Field(default="INFO", description="Logging level")
    log_file: str = Field(default="logs/app.log", description="Log file path")

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "protected_namespaces": ('settings_',),
        "env_prefix": "",
    }

    @property
    def model_provider(self) -> str:
        """Alias for provider to maintain backward compatibility."""
        return self.provider


def get_settings() -> Settings:
    """Get application settings."""
    return Settings()


def ensure_directories(settings: Settings) -> None:
    """Ensure required directories exist."""
    directories = [
        Path(settings.temp_vector_store_path).parent,
        Path(settings.log_file).parent,
        Path(settings.root_folder),
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)


# Global settings instance
settings = get_settings()
ensure_directories(settings)
