"""
Test script to verify the project setup and configuration.
"""
import os
import sys
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported."""
    print("Testing imports...")
    
    try:
        import config
        print("✅ Config module imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import config: {e}")
        return False
    
    try:
        from models.model_factory import ModelFactory, get_models
        print("✅ Model factory imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import model factory: {e}")
        return False
    
    try:
        from core.policy_mappings import POLICY_FIELD_MAPPINGS
        print("✅ Policy mappings imported successfully")
        print(f"   Found {len(POLICY_FIELD_MAPPINGS)} policy types")
    except ImportError as e:
        print(f"❌ Failed to import policy mappings: {e}")
        return False
    
    try:
        from utils.logging_config import setup_logging, get_logger
        print("✅ Logging config imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import logging config: {e}")
        return False
    
    return True


def test_configuration():
    """Test configuration loading."""
    print("\nTesting configuration...")
    
    try:
        from config import settings
        print("✅ Settings loaded successfully")
        print(f"   Model provider: {settings.model_provider}")
        print(f"   Root folder: {settings.root_folder}")
        print(f"   Top K value: {settings.top_k_value}")
        return True
    except Exception as e:
        print(f"❌ Failed to load settings: {e}")
        return False


def test_directories():
    """Test if required directories exist or can be created."""
    print("\nTesting directories...")
    
    from config import settings
    
    directories = [
        Path(settings.temp_vector_store_path).parent,
        Path(settings.log_file).parent,
        Path("data"),
        Path("logs"),
        Path("temp")
    ]
    
    for directory in directories:
        try:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"✅ Directory created/verified: {directory}")
        except Exception as e:
            print(f"❌ Failed to create directory {directory}: {e}")
            return False
    
    return True


def test_dependencies():
    """Test if key dependencies are available."""
    print("\nTesting dependencies...")
    
    dependencies = [
        "langchain",
        "langchain_community", 
        "langchain_core",
        "fitz",  # PyMuPDF
        "faiss",
        "numpy",
        "pandas",
        "pydantic",
        "dotenv"
    ]
    
    missing_deps = []
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep} available")
        except ImportError:
            print(f"❌ {dep} not available")
            missing_deps.append(dep)
    
    if missing_deps:
        print(f"\n⚠️  Missing dependencies: {', '.join(missing_deps)}")
        print("Install with: pip install -r requirements.txt")
        return False
    
    return True


def test_model_providers():
    """Test model provider availability."""
    print("\nTesting model providers...")
    
    # Test Ollama
    try:
        from langchain_ollama import ChatOllama
        print("✅ Ollama provider available")
    except ImportError:
        print("❌ Ollama provider not available")
    
    # Test Hugging Face
    try:
        from langchain_huggingface import ChatHuggingFace
        print("✅ Hugging Face provider available")
    except ImportError:
        print("❌ Hugging Face provider not available")


def main():
    """Run all tests."""
    print("=" * 60)
    print("Insurance Policy Classification System - Setup Test")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_configuration,
        test_directories,
        test_dependencies,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    test_model_providers()  # This is informational only
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ Setup verification completed successfully!")
        print("\nNext steps:")
        print("1. Copy .env.example to .env and configure your settings")
        print("2. Ensure your chosen model provider (Ollama/HF) is set up")
        print("3. Add your PDF documents to the configured data directory")
        print("4. Run: python main.py")
    else:
        print("❌ Setup verification failed!")
        print("Please fix the issues above before running the main application.")
        sys.exit(1)


if __name__ == "__main__":
    main()
