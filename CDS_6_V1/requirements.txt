# Core dependencies
langchain>=0.1.0
langchain-community>=0.0.20
langchain-core>=0.1.0
langchain-ollama>=0.1.0
langchain-huggingface>=0.0.3
transformers>=4.35.0
torch>=2.0.0
sentence-transformers>=2.2.2

# Vector stores and retrievers
faiss-cpu>=1.7.4
rank-bm25>=0.2.2

# PDF processing
PyMuPDF>=1.23.0

# Data handling
numpy>=1.24.0
pandas>=2.0.0

# Configuration and environment
python-dotenv>=1.0.0
pydantic>=2.0.0
pydantic-settings>=2.0.0

# Utilities
tqdm>=4.65.0
pathlib2>=2.3.7

# Optional: For advanced features
scikit-learn>=1.3.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Development dependencies
pytest>=7.0.0
black>=23.0.0
flake8>=6.0.0
