"""
Main application for Insurance Policy Classification System.
Enhanced version with support for both Ollama and Hugging Face models.
"""
import difflib
import json
import os
import re
import shutil
from pathlib import Path
from typing import List, Dict, Any

import fitz
from langchain_community.retrievers import BM25Retriever
from langchain_community.vectorstores import FAISS
from langchain_core.documents import Document
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.vectorstores import VectorStoreRetriever

from config import settings
from models.model_factory import get_models
from core.policy_mappings import POLICY_FIELD_MAPPINGS
from utils.logging_config import setup_logging, get_logger

# Initialize logging
logger = setup_logging(settings)

# Global variables
current_vector_store = None
current_retriever = None
current_bm25_retriever = None


def calculate_chunk_relevance_score(chunk: Document, policy_keywords: List[str]) -> float:
    """
    Calculate relevance score for a chunk based on policy-specific keywords
    """
    content_lower = chunk.page_content.lower()

    # Core policy identification terms
    high_value_keywords = [
        'applied', 'policy', 'coverage', 'liability', 'insurance',
        'aviation', 'entertainment', 'financial', 'commercial', 'excess',
        'underwriter', 'certificate', 'declaration'
    ]

    # Policy type specific terms
    policy_specific_keywords = [
        'general liability', 'corporate aircraft', 'sport excess',
        'management liability', 'professional liability', 'commercial excess',
        'risk logistics', 'outfitters guides', 'commercial property'
    ]

    score = 0.0
    total_words = len(content_lower.split())

    if total_words == 0:
        return 0.0

    # High-value keyword scoring
    for keyword in high_value_keywords:
        if keyword in content_lower:
            score += 0.15

    # Policy-specific keyword scoring (higher weight)
    for keyword in policy_specific_keywords:
        if keyword in content_lower:
            score += 0.25

    # Boost score for early pages (often contain key policy info)
    page_num = chunk.metadata.get('page_number', 999)
    if page_num <= 2:
        score += 0.2
    elif page_num <= 5:
        score += 0.1

    # Penalty for very short chunks (likely incomplete information)
    if len(chunk.page_content.strip()) < 200:
        score -= 0.1

    # Bonus for chunks with structured content (contains policy numbers, dates, etc.)
    if re.search(r'\b\d{4,}\b', content_lower):  # Policy numbers, years
        score += 0.1

    return min(score, 1.0)  # Cap at 1.0


def filter_and_deduplicate_context(chunks: List[Document], max_chars: int = None) -> List[Document]:
    """
    Enhanced context filtering with relevance scoring and deduplication
    """
    if max_chars is None:
        max_chars = settings.max_context_chars
        
    if not chunks:
        return []

    # Step 1: Calculate relevance scores
    policy_keywords = ['applied', 'insurance', 'policy', 'coverage', 'liability']
    scored_chunks = []

    for chunk in chunks:
        relevance_score = calculate_chunk_relevance_score(chunk, policy_keywords)
        if relevance_score >= settings.min_chunk_relevance_score:
            chunk.metadata['relevance_score'] = relevance_score
            scored_chunks.append(chunk)

    # Step 2: Sort by relevance score (descending)
    scored_chunks.sort(key=lambda x: x.metadata.get('relevance_score', 0), reverse=True)

    # Step 3: Deduplication based on content similarity
    if settings.enable_context_deduplication:
        deduplicated_chunks = []
        for chunk in scored_chunks:
            is_duplicate = False
            chunk_words = set(chunk.page_content.lower().split())

            for existing_chunk in deduplicated_chunks:
                existing_words = set(existing_chunk.page_content.lower().split())

                # Calculate Jaccard similarity
                intersection = len(chunk_words.intersection(existing_words))
                union = len(chunk_words.union(existing_words))
                similarity = intersection / union if union > 0 else 0

                if similarity > 0.7:  # 70% similarity threshold
                    is_duplicate = True
                    break

            if not is_duplicate:
                deduplicated_chunks.append(chunk)

        scored_chunks = deduplicated_chunks

    # Step 4: Select chunks within character limit
    selected_chunks = []
    total_chars = 0

    for chunk in scored_chunks:
        chunk_chars = len(chunk.page_content)
        if total_chars + chunk_chars <= max_chars:
            selected_chunks.append(chunk)
            total_chars += chunk_chars
        else:
            # Try to fit a truncated version if it's a high-relevance chunk
            if chunk.metadata.get('relevance_score', 0) > 0.7:
                remaining_chars = max_chars - total_chars
                if remaining_chars > 500:  # Only if we have meaningful space left
                    truncated_content = chunk.page_content[:remaining_chars - 100] + "..."
                    truncated_chunk = Document(
                        page_content=truncated_content,
                        metadata=chunk.metadata.copy()
                    )
                    selected_chunks.append(truncated_chunk)
                    break
            else:
                break

    logger.info(f"Context filtering results:")
    logger.info(f"   Original chunks: {len(chunks)}")
    logger.info(f"   After relevance filtering: {len(scored_chunks)}")
    logger.info(f"   Final selected: {len(selected_chunks)}")
    logger.info(f"   Total characters: {sum(len(c.page_content) for c in selected_chunks)}")

    return selected_chunks


def create_focused_prompt_system():
    """
    Create a more focused prompt system that enforces JSON schema compliance
    """
    system_prompt = """You are an expert insurance policy classifier. Your ONLY task is to classify insurance policies into predefined categories.

CRITICAL RULES:
1. You MUST respond with ONLY a valid JSON object
2. Do NOT include any text before or after the JSON
3. Do NOT use markdown code blocks or backticks
4. If uncertain, choose the most likely category and set confidence accordingly

Policy Categories (choose exactly one):
1. Applied Aviation Commercial General Liability
2. Applied Aviation Corporate Aircraft  
3. Applied Entertainment Sport Excess Liability
4. Applied Entertainment Sports General Liability
5. Applied Financial Lines Excess Liability
6. Applied Financial Lines Management Liability
7. Applied Financial Lines Professional Liability
8. Applied Specialty Underwriters Commercial Excess Liability
9. Applied United Risk Logistics
10. Outfitters & Guides Commercial General Liability
11. Shared & Layered (Commercial Property)

Required JSON format:
{
  "full_policy_name": "exact policy name from list above",
  "confidence_score": 0.85
}"""

    return system_prompt


def validate_and_fix_json_response(raw_response: str) -> Dict[str, Any]:
    """
    Enhanced JSON validation and repair
    """
    if not raw_response or not raw_response.strip():
        return {"error": "Empty response", "full_policy_name": "", "confidence_score": 0.0}

    # Clean the response
    cleaned_response = raw_response.strip()

    # Remove common markdown artifacts
    cleaned_response = re.sub(r'```json\s*', '', cleaned_response)
    cleaned_response = re.sub(r'```\s*$', '', cleaned_response)
    cleaned_response = cleaned_response.strip()

    # Try to extract JSON object
    json_patterns = [
        r'\{[^{}]*"full_policy_name"[^{}]*\}',  # Look for objects with required field
        r'\{.*?\}',  # Any JSON object
    ]

    parsed_json = None

    for pattern in json_patterns:
        matches = re.findall(pattern, cleaned_response, re.DOTALL)
        for match in matches:
            try:
                candidate = json.loads(match)
                if isinstance(candidate, dict):
                    parsed_json = candidate
                    break
            except:
                continue
        if parsed_json:
            break

    # If no JSON found, try parsing the whole response
    if not parsed_json:
        try:
            parsed_json = json.loads(cleaned_response)
        except:
            pass

    # Validation and repair
    if not parsed_json or not isinstance(parsed_json, dict):
        return {"error": "No valid JSON found", "raw_response": raw_response[:200]}

    # Ensure required fields exist
    if "full_policy_name" not in parsed_json:
        parsed_json["full_policy_name"] = ""

    if "confidence_score" not in parsed_json:
        parsed_json["confidence_score"] = 0.5

    # Validate confidence score
    try:
        confidence = float(parsed_json["confidence_score"])
        parsed_json["confidence_score"] = max(0.0, min(1.0, confidence))
    except:
        parsed_json["confidence_score"] = 0.5

    return parsed_json


def load_pdf_documents(file_path: str) -> List[Document]:
    """Load and process a PDF file into Document objects."""
    documents = []
    if not os.path.exists(file_path):
        logger.error(f"File not found at {file_path}")
        return documents
    try:
        doc = fitz.open(file_path)
        for index, page in enumerate(doc):
            text = page.get_text()
            if text.strip():
                documents.append(Document(
                    page_content=text,
                    metadata={"source": os.path.basename(file_path), "page_number": index + 1}
                ))
        doc.close()
        logger.info(f"Successfully processed {len(documents)} pages from '{os.path.basename(file_path)}'.")
    except Exception as e:
        logger.error(f"Error processing PDF {file_path}: {e}")
    return documents


def create_vector_store(documents: List[Document], embeddings) -> FAISS:
    """Create FAISS vector store from documents."""
    if not documents:
        raise ValueError("No documents provided for vector store creation")
    try:
        vector_store = FAISS.from_documents(documents, embeddings)
        return vector_store
    except Exception as e:
        logger.error(f"Error creating FAISS vector store: {e}")
        raise


def create_retriever_from_vector_store(vector_store: FAISS,
                                       search_kwargs: Dict[str, Any] = None) -> VectorStoreRetriever:
    """Create a LangChain retriever from FAISS vector store."""
    if search_kwargs is None:
        search_kwargs = {"k": settings.top_k_value}
    try:
        retriever = vector_store.as_retriever(search_kwargs=search_kwargs)
        logger.info(f"LangChain retriever created successfully with search_kwargs: {search_kwargs}")
        return retriever
    except Exception as e:
        logger.error(f"Error creating retriever from vector store: {e}")
        raise


def create_bm25_retriever(documents: List[Document], k: int = None) -> BM25Retriever:
    """Create a BM25 retriever from documents."""
    if k is None:
        k = settings.top_k_value
    try:
        texts = [doc.page_content for doc in documents]
        bm25_retriever = BM25Retriever.from_texts(
            texts,
            metadatas=[doc.metadata for doc in documents],
            k=k
        )
        logger.info(f"BM25 retriever created successfully with k={k}")
        return bm25_retriever
    except Exception as e:
        logger.error(f"Error creating BM25 retriever: {e}")
        raise


def retrieve_context_hybrid(query: str, vector_weight: float = 0.7, bm25_weight: float = 0.3) -> list:
    """Hybrid retrieval combining vector similarity and BM25 results."""
    global current_retriever, current_bm25_retriever
    all_docs = []

    if current_retriever and vector_weight > 0:
        try:
            vector_docs = current_retriever.invoke(query)
            for doc in vector_docs:
                doc.metadata['retrieval_method'] = 'vector'
                doc.metadata['retrieval_weight'] = vector_weight
            all_docs.extend(vector_docs)
        except Exception as e:
            logger.error(f"Error in vector retrieval: {e}")

    if current_bm25_retriever and bm25_weight > 0:
        try:
            bm25_docs = current_bm25_retriever.invoke(query)
            for doc in bm25_docs:
                doc.metadata['retrieval_method'] = 'bm25'
                doc.metadata['retrieval_weight'] = bm25_weight
            all_docs.extend(bm25_docs)
        except Exception as e:
            logger.error(f"Error in BM25 retrieval: {e}")

    if all_docs:
        deduplicated_docs = sort_and_deduplicate_chunks(all_docs)
        return deduplicated_docs
    return []


def sort_and_deduplicate_chunks(chunks):
    """Sort and deduplicate chunks based on source and page number."""
    try:
        seen = set()
        unique_chunks = []
        for doc in chunks:
            source = doc.metadata.get('source')
            page_number = doc.metadata.get('page_number', float('inf'))
            key = (source, page_number)
            if key not in seen:
                seen.add(key)
                unique_chunks.append(doc)

        sorted_documents = sorted(
            unique_chunks,
            key=lambda doc: (
                doc.metadata.get('source'),
                doc.metadata.get('page_number', float('inf'))
            )
        )
        return sorted_documents
    except Exception as e:
        logger.error(f"Unable to sort and deduplicate attachments: {e}")
        return chunks


def cleanup_vector_store():
    """Clean up temporary vector store files."""
    global current_vector_store, current_retriever, current_bm25_retriever
    current_vector_store = None
    current_retriever = None
    current_bm25_retriever = None
    if os.path.exists(settings.temp_vector_store_path):
        try:
            shutil.rmtree(settings.temp_vector_store_path)
        except Exception as e:
            logger.error(f"Error removing temporary vector store: {e}")


def extract_key_fields(llm_prediction: str, llm=None, context: str = None) -> Dict[str, Any]:
    """
    Extract key fields based on LLM prediction matching POLICY_FIELD_MAPPINGS.

    Args:
        llm_prediction: The predicted policy type from LLM
        llm: Optional LLM instance for field extraction
        context: Optional context text for field extraction

    Returns:
        Dictionary containing extraction results or field information
    """
    logger.info(f"Checking prediction '{llm_prediction}' against POLICY_FIELD_MAPPINGS...")

    # Step 1: Find matching policy type
    best_match = None
    best_similarity = 0.0
    similarity_threshold = 0.7

    for policy_type in POLICY_FIELD_MAPPINGS.keys():
        similarity = difflib.SequenceMatcher(None, llm_prediction.strip().lower(), policy_type.lower()).ratio()
        if similarity > best_similarity:
            best_similarity = similarity
            best_match = policy_type

    # Step 2: Check if we found a good match
    if best_similarity < similarity_threshold:
        logger.warning(f"No matching policy type found. Best match: '{best_match}' (similarity: {best_similarity:.3f})")
        return {
            "status": "no_match",
            "prediction": llm_prediction,
            "best_match": best_match,
            "similarity": best_similarity,
            "fields": None
        }

    logger.info(f"Found matching policy: '{best_match}' (similarity: {best_similarity:.3f})")
    fields_to_extract = POLICY_FIELD_MAPPINGS[best_match]

    # Step 3: If no LLM or context provided, just return the fields list
    if llm is None or context is None:
        logger.info(f"Returning {len(fields_to_extract)} fields to extract (no LLM extraction)")
        return {
            "status": "fields_identified",
            "matched_policy": best_match,
            "similarity": best_similarity,
            "fields_to_extract": fields_to_extract,
            "field_count": len(fields_to_extract)
        }

    # Step 4: Make LLM call to extract actual field values
    logger.info(f"Making LLM call to extract {len(fields_to_extract)} fields...")

    # Create field extraction prompt
    fields_list = "\n".join([f"- {field}" for field in fields_to_extract])

    system_prompt = f"""You are an expert at extracting specific data fields from insurance policy documents.

TASK: Extract the following fields from a {best_match} insurance policy document:

FIELDS TO EXTRACT:
{fields_list}

EXTRACTION RULES:
1. Extract exact values as they appear in the document
2. If a field is not found, set its value to null
3. For monetary amounts, include currency symbols and formatting as shown
4. For dates, preserve the original format
5. For addresses, include the complete address as shown
6. For policy numbers, include any prefixes/suffixes

CRITICAL: Respond with ONLY valid JSON, no other text:
{{
  "extracted_fields": {{
    "Policy Number": "value or null",
    "Named Insured": "value or null",
    ...
  }},
  "extraction_confidence": 0.85,
  "fields_found": 5,
  "total_fields": {len(fields_to_extract)}
}}"""

    human_prompt = f"""Extract the required fields from this {best_match} document:

{context}

Respond with only the JSON extraction result."""

    # Step 5: LLM invocation with retry logic
    max_retries = 2
    for attempt in range(max_retries):
        try:
            logger.info(f"Field extraction attempt {attempt + 1}/{max_retries}")

            chat_history = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=human_prompt)
            ]

            raw_response = llm.invoke(chat_history)
            raw_content = raw_response.content if hasattr(raw_response, 'content') else str(raw_response)

            logger.info(f"Raw extraction response (first 200 chars): {raw_content[:200]}")

            # Parse and validate JSON response
            extraction_result = validate_and_fix_json_response(raw_content)

            if extraction_result and not extraction_result.get("error"):
                logger.info("Field extraction completed successfully")
                return {
                    "status": "extraction_completed",
                    "matched_policy": best_match,
                    "similarity": best_similarity,
                    "extraction_result": extraction_result,
                    "fields_requested": len(fields_to_extract)
                }
            else:
                logger.warning(f"Attempt {attempt + 1} produced invalid extraction result: {extraction_result}")

        except Exception as e:
            logger.error(f"Field extraction attempt {attempt + 1} failed: {str(e)}")

        if attempt < max_retries - 1:
            logger.info("Retrying field extraction...")

    # If all attempts failed
    logger.error(f"Field extraction failed after {max_retries} attempts")
    return {
        "status": "extraction_failed",
        "matched_policy": best_match,
        "similarity": best_similarity,
        "fields_to_extract": fields_to_extract,
        "error": "Failed to extract fields after multiple attempts"
    }


def enhanced_classify_insurance_policy(file_path: str, embeddings, llm, expected_policy: str) -> Dict[str, Any]:
    """
    Enhanced classification with improved context management and schema validation
    """
    global current_vector_store, current_retriever, current_bm25_retriever

    logger.info(f"Enhanced Processing: {os.path.basename(file_path)}")
    logger.info(f"Expected: {expected_policy}")

    # 1. Load documents
    documents = load_pdf_documents(file_path)
    if not documents:
        return {
            "file_path": file_path,
            "error": "Failed to load documents",
            "classification": None
        }

    # 2. Create vector store and retrievers
    try:
        current_vector_store = create_vector_store(documents, embeddings)
        current_retriever = create_retriever_from_vector_store(current_vector_store)
        current_bm25_retriever = create_bm25_retriever(documents, k=settings.top_k_value)
        logger.info("Vector store and retrievers created successfully")
    except Exception as e:
        cleanup_vector_store()
        return {
            "file_path": file_path,
            "error": f"Failed to create vector store or retriever: {str(e)}",
            "classification": None
        }

    # 3. Strategic context retrieval - SINGLE focused query to avoid confusion
    primary_query = "Applied insurance policy type classification coverage liability underwriter"

    logger.info("Retrieving context with focused query...")
    comprehensive_context = []

    # Use hybrid retrieval for best results
    retrieved_docs = retrieve_context_hybrid(primary_query, vector_weight=0.6, bm25_weight=0.4)
    comprehensive_context.extend(retrieved_docs)

    # Apply enhanced filtering and deduplication
    filtered_context = filter_and_deduplicate_context(comprehensive_context, settings.max_context_chars)

    if not filtered_context:
        logger.warning("No relevant context found after filtering")
        cleanup_vector_store()
        return {
            "file_path": file_path,
            "error": "Could not retrieve sufficient relevant context",
            "classification": None
        }

    # 4. Prepare context string
    context_parts = []
    for doc in filtered_context:
        page_num = doc.metadata.get('page_number', 'Unknown')
        relevance = doc.metadata.get('relevance_score', 0)
        content = doc.page_content.strip()
        context_parts.append(f"[Page {page_num}, Relevance: {relevance:.2f}]: {content}")

    final_context = "\n\n".join(context_parts)
    logger.info(f"Final context length: {len(final_context)} characters")

    # 5. Create focused prompts
    system_prompt = create_focused_prompt_system()

    human_prompt = f"""Based on the insurance policy document below, classify it into exactly ONE of the 11 predefined categories.

Insurance Policy Document:
{final_context}

Respond with ONLY the JSON object, no other text."""

    # 6. LLM invocation with retry logic
    max_retries = 2
    for attempt in range(max_retries):
        try:
            logger.info(f"LLM invocation attempt {attempt + 1}/{max_retries}")

            chat_history = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=human_prompt)
            ]

            raw_response = llm.invoke(chat_history)
            raw_content = raw_response.content if hasattr(raw_response, 'content') else str(raw_response)

            logger.info(f"Raw LLM response (first 300 chars): {raw_content[:300]}")

            # Enhanced JSON validation and repair
            classification_result = validate_and_fix_json_response(raw_content)

            # Check if we got a valid classification
            if (classification_result.get("full_policy_name") and
                    not classification_result.get("error") and
                    classification_result.get("confidence_score", 0) > 0):

                # Validate against expected policy
                predicted_policy = classification_result["full_policy_name"]
                similarity = difflib.SequenceMatcher(None, predicted_policy.strip(), expected_policy).ratio()

                classification_result['expected_policy'] = expected_policy
                classification_result['similarity_score'] = similarity
                classification_result['result'] = '✅Pass' if similarity >= 0.7 else '❌Fail'

                logger.info(f"Classification Result: {classification_result['result']}")
                logger.info(f"   Similarity to expected: {similarity:.3f}")

                # Extract key fields using the new function
                field_extraction_result = extract_key_fields(predicted_policy, llm, final_context)

                cleanup_vector_store()
                return {
                    "file_path": file_path,
                    "classification": classification_result,
                    "field_extraction": field_extraction_result,
                    "context_length": len(final_context),
                    "chunks_used": len(filtered_context)
                }
            else:
                logger.warning(f"Attempt {attempt + 1} produced invalid result: {classification_result}")
                if attempt < max_retries - 1:
                    logger.info("Retrying with adjusted parameters...")
                    continue

        except Exception as e:
            logger.error(f"Error in attempt {attempt + 1}: {str(e)}")
            if attempt < max_retries - 1:
                continue

    # If all attempts failed
    cleanup_vector_store()
    return {
        "file_path": file_path,
        "error": "Failed to get valid classification after multiple attempts",
        "classification": classification_result if 'classification_result' in locals() else None
    }


def process_all_files_enhanced(root_folder: str):
    """Process all PDF files with enhanced classification system."""
    logger.info(f"Initializing models...")
    try:
        llm, embeddings = get_models(settings)
    except Exception as e:
        logger.error(f"Failed to initialize models: {e}")
        return None

    root_path = Path(root_folder)
    if not root_path.exists():
        logger.error(f"Root folder {root_folder} does not exist.")
        return None

    results = []
    total_files = 0
    successful_classifications = 0

    for folder in sorted(root_path.rglob("*")):
        if folder.is_dir():
            pdf_files = list(folder.glob("*.pdf"))
            if not pdf_files:
                continue

            logger.info(f"Found {len(pdf_files)} PDF(s) in folder: {folder}")
            for pdf_file_path in pdf_files:
                total_files += 1
                file_path_str = str(pdf_file_path)
                expected_policy = pdf_file_path.parent.name

                logger.info(f"[{total_files}] Processing '{pdf_file_path.name}'...")
                result = enhanced_classify_insurance_policy(file_path_str, embeddings, llm, expected_policy)
                results.append(result)

                # Track success rate
                if (result.get('classification') and
                        not result.get('error') and
                        result.get('classification', {}).get('result') == '✅Pass'):
                    successful_classifications += 1

    # Save results
    output_file = f"enhanced_insurance_classification_{settings.model_provider}_{settings.top_k_value}.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)

    # Summary
    logger.info("=" * 60)
    logger.info("ENHANCED CLASSIFICATION RESULTS")
    logger.info("=" * 60)
    logger.info(f"Total files processed: {total_files}")
    logger.info(f"Successful classifications: {successful_classifications}")
    logger.info(f"Success rate: {(successful_classifications / total_files) * 100:.1f}%")
    logger.info(f"Results saved to: {output_file}")
    logger.info("=" * 60)

    return results


def main():
    """Main entry point for the application."""
    logger.info("Starting Insurance Policy Classification System")
    logger.info(f"Model Provider: {settings.model_provider}")
    logger.info(f"Root Folder: {settings.root_folder}")

    # Check if root folder exists
    if not os.path.exists(settings.root_folder):
        logger.error(f"Root folder '{settings.root_folder}' does not exist.")
        logger.info("Please update the ROOT_FOLDER in your .env file or create the directory.")
        return

    # Process all files
    results = process_all_files_enhanced(settings.root_folder)

    if results:
        logger.info("Processing completed successfully!")
    else:
        logger.error("Processing failed!")


if __name__ == "__main__":
    main()
