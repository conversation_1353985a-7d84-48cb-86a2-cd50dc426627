"""
Utility script to switch between model providers (Ollama/Hugging Face).
"""
import os
import sys
from pathlib import Path


def update_env_file(provider: str):
    """Update the .env file with the specified provider."""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("❌ .env file not found. Please copy .env.example to .env first.")
        return False
    
    # Read current content
    with open(env_file, 'r') as f:
        lines = f.readlines()
    
    # Update PROVIDER line
    updated_lines = []
    for line in lines:
        if line.startswith("PROVIDER="):
            updated_lines.append(f"PROVIDER={provider}\n")
        else:
            updated_lines.append(line)
    
    # Write back
    with open(env_file, 'w') as f:
        f.writelines(updated_lines)
    
    print(f"✅ Updated PROVIDER to: {provider}")
    return True


def show_current_config():
    """Show current configuration."""
    try:
        from config import settings
        print(f"Current model provider: {settings.model_provider}")
        
        if settings.model_provider.lower() == "ollama":
            print(f"Ollama model: {settings.ollama_model_name}")
            print(f"Ollama embeddings: {settings.ollama_embedding_model}")
            print(f"Ollama URL: {settings.ollama_base_url}")
        elif settings.model_provider.lower() == "huggingface":
            print(f"HF model: {settings.hf_model_name}")
            print(f"HF embeddings: {settings.hf_embedding_model}")
            print(f"HF token set: {'Yes' if settings.huggingface_api_token else 'No'}")
    except Exception as e:
        print(f"❌ Error reading configuration: {e}")


def check_ollama_availability():
    """Check if Ollama is available."""
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get("models", [])
            print(f"✅ Ollama is running with {len(models)} models available")
            for model in models[:5]:  # Show first 5 models
                print(f"   - {model['name']}")
            return True
        else:
            print("❌ Ollama is running but API returned error")
            return False
    except Exception as e:
        print(f"❌ Ollama not available: {e}")
        return False


def check_huggingface_availability():
    """Check if Hugging Face is available."""
    try:
        from config import settings
        if settings.huggingface_api_token:
            print("✅ Hugging Face token is configured")
            return True
        else:
            print("❌ Hugging Face token not set in .env file")
            return False
    except Exception as e:
        print(f"❌ Error checking Hugging Face config: {e}")
        return False


def main():
    """Main function."""
    if len(sys.argv) != 2:
        print("Usage: python switch_model.py [ollama|huggingface|status]")
        print("\nCommands:")
        print("  ollama      - Switch to Ollama provider")
        print("  huggingface - Switch to Hugging Face provider")
        print("  status      - Show current configuration and availability")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "status":
        print("=" * 50)
        print("Model Provider Status")
        print("=" * 50)
        show_current_config()
        print("\nProvider Availability:")
        print("Ollama:")
        check_ollama_availability()
        print("\nHugging Face:")
        check_huggingface_availability()
        
    elif command == "ollama":
        print("Switching to Ollama provider...")
        if update_env_file("ollama"):
            print("\nChecking Ollama availability...")
            if check_ollama_availability():
                print("✅ Ready to use Ollama!")
            else:
                print("⚠️  Ollama not available. Please start Ollama and pull required models:")
                print("   ollama pull deepseek-r1:14b")
                print("   ollama pull mxbai-embed-large:latest")
        
    elif command == "huggingface":
        print("Switching to Hugging Face provider...")
        if update_env_file("huggingface"):
            print("\nChecking Hugging Face configuration...")
            if check_huggingface_availability():
                print("✅ Ready to use Hugging Face!")
            else:
                print("⚠️  Please set HUGGINGFACE_API_TOKEN in your .env file")
                print("   Get your token from: https://huggingface.co/settings/tokens")
        
    else:
        print(f"❌ Unknown command: {command}")
        sys.exit(1)


if __name__ == "__main__":
    main()
