# Insurance Policy Classification System (CDS_6_V1)

An enhanced insurance policy classification system with support for both Ollama and Hugging Face models, featuring vector search with FAISS and BM25 retrievers for accurate policy type identification and field extraction.

## Features

- **Multi-Model Support**: Works with both Ollama and Hugging Face models
- **Hybrid Retrieval**: Combines FAISS vector search and BM25 for optimal context retrieval
- **Field Extraction**: Automatically extracts relevant fields based on policy type
- **Enhanced Context Filtering**: Relevance scoring and deduplication for better accuracy
- **Configurable**: Environment-based configuration with sensible defaults
- **Comprehensive Logging**: Detailed logging for debugging and monitoring

## Project Structure

```
CDS_6_V1/
├── main.py                 # Main application entry point
├── config.py              # Configuration management
├── requirements.txt       # Python dependencies
├── .env.example          # Environment variables template
├── README.md             # This file
├── models/
│   ├── __init__.py
│   └── model_factory.py  # Model factory for Ollama/HF models
├── core/
│   ├── __init__.py
│   └── policy_mappings.py # Policy field mappings
├── utils/
│   ├── __init__.py
│   └── logging_config.py # Logging configuration
├── data/                 # Data directory (created automatically)
├── logs/                 # Logs directory (created automatically)
└── temp/                 # Temporary files (created automatically)
```

## Installation

1. **Clone or create the project directory:**
   ```bash
   cd CDS_6_V1
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your specific configuration
   ```

## Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure the following:

#### Model Provider Selection
```bash
# Choose 'ollama' or 'huggingface'
MODEL_PROVIDER=ollama
```

#### Ollama Configuration (if using Ollama)
```bash
OLLAMA_MODEL_NAME=deepseek-r1:14b
OLLAMA_EMBEDDING_MODEL=mxbai-embed-large:latest
OLLAMA_BASE_URL=http://localhost:11434
```

#### Hugging Face Configuration (if using HF)
```bash
HF_MODEL_NAME=microsoft/DialoGPT-medium
HF_EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
HUGGINGFACE_API_TOKEN=your_hf_token_here
```

#### Data Paths
```bash
ROOT_FOLDER=/path/to/your/policy/documents
TEMP_VECTOR_STORE_PATH=./temp/faiss_collection
```

#### Processing Parameters
```bash
TOP_K_VALUE=3
MAX_CONTEXT_CHARS=22000
MIN_CHUNK_RELEVANCE_SCORE=0.3
ENABLE_CONTEXT_DEDUPLICATION=true
ENABLE_SCHEMA_VALIDATION=true
```

## Usage

### Basic Usage

1. **Ensure your models are available:**
   - For Ollama: Make sure Ollama is running and models are pulled
   - For Hugging Face: Ensure you have the required API token

2. **Prepare your data:**
   - Organize PDF files in folders named after policy types
   - Update `ROOT_FOLDER` in `.env` to point to your data directory

3. **Run the application:**
   ```bash
   python main.py
   ```

### Supported Policy Types

The system can classify the following insurance policy types:

1. Applied Aviation Commercial General Liability
2. Applied Aviation Corporate Aircraft
3. Applied Entertainment Sport Excess Liability
4. Applied Entertainment Sports General Liability
5. Applied Financial Lines Excess Liability
6. Applied Financial Lines Management Liability
7. Applied Financial Lines Professional Liability
8. Applied Specialty Underwriters Commercial Excess Liability
9. Applied United Risk Logistics
10. Outfitters & Guides Commercial General Liability
11. Commercial Property

### Output

The system generates:
- JSON results file with classification and field extraction results
- Detailed logs in the `logs/` directory
- Success rate and performance metrics

## Model Setup

### Ollama Setup

1. **Install Ollama:**
   ```bash
   # Follow instructions at https://ollama.ai
   ```

2. **Pull required models:**
   ```bash
   ollama pull deepseek-r1:14b
   ollama pull mxbai-embed-large:latest
   ```

### Hugging Face Setup

1. **Get API token:**
   - Visit https://huggingface.co/settings/tokens
   - Create a new token with read permissions

2. **Set token in environment:**
   ```bash
   export HUGGINGFACE_API_TOKEN=your_token_here
   ```

## Development

### Adding New Policy Types

1. Update `core/policy_mappings.py` with new policy fields
2. Update the prompt system in `main.py` to include new categories
3. Test with sample documents

### Customizing Models

1. Modify `models/model_factory.py` to add new model providers
2. Update configuration in `config.py`
3. Add corresponding environment variables

## Troubleshooting

### Common Issues

1. **Model not found:**
   - Ensure Ollama models are pulled or HF models are accessible
   - Check API tokens and network connectivity

2. **Memory issues:**
   - Reduce `MAX_CONTEXT_CHARS` in configuration
   - Use smaller models or enable GPU acceleration

3. **Poor classification accuracy:**
   - Adjust `MIN_CHUNK_RELEVANCE_SCORE`
   - Modify retrieval weights in hybrid search
   - Check document quality and formatting

### Logging

Check logs in `logs/app.log` for detailed error information and processing steps.

## License

This project is for internal use and development purposes.
